# WHMCS Coolify Plugin - Setup Guide

## Prerequisites

- WHMCS 7.0+ with PHP 7.4+ or PHP 8.x
- Active Coolify instance with API access
- Coolify API Bearer Token with appropriate permissions

## Step 1: Install the Plugin

1. Upload the entire `modules/servers/coolify/` folder to your WHMCS installation
2. Ensure proper file permissions (644 for files, 755 for directories)

## Step 2: Configure Coolify Server in WHMCS

1. Go to **WHMCS Admin → Setup → Products/Services → Servers**
2. Click **Add New Server**
3. Fill in the following:
   - **Name**: Your Coolify Server (e.g., "Main Coolify")
   - **Hostname**: Your Coolify domain (e.g., coolify.yourdomain.com)
   - **Type**: Select "Coolify n8n"
   - **Access Hash**: Your Coolify API Bearer Token
   - **Secure**: Check if using HTTPS

4. Configure the module settings:
   - **API Token**: Leave blank (uses Access Hash)
   - **Project UUID**: Your Coolify project UUID
   - **Environment Name**: production (or your environment)
   - **Server UUID**: Your Coolify server UUID
   - **Destination UUID**: Your deployment destination UUID
   - **Base Domain**: Domain for customer subdomains (e.g., customers.yourdomain.com)
   - **n8n Docker Image**: n8nio/n8n:latest
   - **Memory Limit (MB)**: 512
   - **Coolify Instance URL**: https://coolify.yourdomain.com

5. Click **Test Connection** to verify the configuration
6. Save the server configuration

## Step 3: Create Product

1. Go to **WHMCS Admin → Setup → Products/Services → Products/Services**
2. Click **Create a New Product**
3. Configure the product:
   - **Product Type**: Other
   - **Product Group**: Create or select appropriate group
   - **Product Name**: n8n Workflow Automation
   - **Description**: Add customer-facing description

4. Go to the **Module Settings** tab:
   - **Module Name**: Select "Coolify n8n"
   - **Server**: Select your Coolify server
   - Configure any additional options as needed

5. Set pricing and save the product

## Step 4: Test the Setup

1. Create a test order for the n8n product
2. Process the order and provision the service
3. Check the service in **WHMCS Admin → Clients → View/Search Clients**
4. Verify the n8n instance is created in Coolify
5. Test customer access to the n8n dashboard

## Getting Coolify Configuration Details

### Finding Your Project UUID:
1. Log into your Coolify dashboard
2. Go to Projects
3. Click on your project
4. Copy the UUID from the URL or project details

### Finding Your Server UUID:
1. In Coolify, go to Servers
2. Click on your server
3. Copy the UUID from the URL or server details

### Finding Your Destination UUID:
1. In your Coolify server details
2. Go to Destinations
3. Copy the UUID of your Docker destination

### Creating API Token:
1. In Coolify, go to Security → API Tokens
2. Create a new token with appropriate permissions
3. Copy the Bearer token for use in WHMCS

## Troubleshooting

### Connection Test Fails:
- Verify Coolify URL is correct (without /api/v1)
- Check API token has proper permissions
- Ensure Coolify is accessible from WHMCS server
- Check firewall settings

### Service Creation Fails:
- Verify all UUIDs are correct
- Check Coolify server has available resources
- Ensure destination is properly configured
- Review WHMCS Module Log for detailed errors

### Customer Cannot Access n8n:
- Verify domain DNS is configured correctly
- Check SSL certificate generation in Coolify
- Ensure n8n container is running
- Verify basic auth credentials

## Security Considerations

1. **API Token Security**: Store API tokens securely and rotate regularly
2. **Domain Security**: Use proper SSL certificates for all domains
3. **Access Control**: Limit API token permissions to minimum required
4. **Network Security**: Ensure proper firewall rules between WHMCS and Coolify

## Monitoring and Maintenance

1. **Regular Backups**: Backup n8n data volumes regularly
2. **Update Monitoring**: Keep n8n images updated
3. **Resource Monitoring**: Monitor server resources and scaling
4. **Log Monitoring**: Review WHMCS module logs regularly

## Customer Instructions Template

You can provide this to your customers:

---

### Accessing Your n8n Instance

Your n8n workflow automation instance has been provisioned and is ready to use.

**Access Details:**
- URL: https://[username].[yourdomain.com]
- Username: admin
- Password: [automatically generated]

**Getting Started:**
1. Click the "Open n8n Dashboard" button in your service details
2. Log in with the provided credentials
3. Start creating your first workflow
4. Explore the extensive library of integrations

**Support:**
If you need help with n8n or experience any issues, please contact our support team.

---

## Advanced Configuration

### Custom Docker Images:
You can modify the default n8n image in the server configuration to use:
- Specific n8n versions
- Custom n8n builds with additional nodes
- Enterprise n8n versions

### Resource Limits:
Adjust memory limits based on customer needs:
- Basic: 512MB
- Professional: 1GB
- Enterprise: 2GB+

### Environment Variables:
The plugin supports custom environment variables for advanced n8n configuration.

## Support

For technical support with this plugin:
1. Check WHMCS Module Logs first
2. Verify Coolify API connectivity
3. Review this documentation
4. Contact your system administrator

Remember to test thoroughly in a staging environment before deploying to production!

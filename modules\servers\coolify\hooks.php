<?php
/**
 * WHMCS Coolify Module Hooks
 *
 * This file handles module setup, custom field creation, and other initialization tasks
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use Illuminate\Database\Capsule\Manager as Capsule;

add_hook('ModuleActivate', 1, function($vars) {
    if ($vars['module'] == 'coolify') {
        coolify_setupCustomFields();
        coolify_createDatabaseTables();
    }
});

add_hook('ModuleDeactivate', 1, function($vars) {
    if ($vars['module'] == 'coolify') {
        // Optionally clean up when module is deactivated
        // coolify_cleanupCustomFields();
    }
});

add_hook('PreModuleCreate', 1, function($vars) {
    if ($vars['producttype'] == 'server' && $vars['servertype'] == 'coolify') {
        // Pre-creation hook - can be used for validation
        return $vars;
    }
});

add_hook('AfterModuleCreate', 1, function($vars) {
    if ($vars['producttype'] == 'server' && $vars['servertype'] == 'coolify') {
        // Post-creation hook - can be used for additional setup
        coolify_postCreateSetup($vars);
    }
});

add_hook('AfterModuleTerminate', 1, function($vars) {
    if ($vars['producttype'] == 'server' && $vars['servertype'] == 'coolify') {
        // Clean up after termination
        coolify_postTerminateCleanup($vars);
    }
});

/**
 * Setup custom fields required for the module
 */
function coolify_setupCustomFields()
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Get all products that use coolify server module
        $stmt = $pdo->prepare("SELECT id FROM tblproducts WHERE servertype = 'coolify'");
        $stmt->execute();
        $products = $stmt->fetchAll(PDO::FETCH_ASSOC);

        foreach ($products as $product) {
            $productId = $product['id'];

            // Check if custom field already exists
            $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = 'coolify_service_uuid'");
            $stmt->execute([$productId]);
            $existingField = $stmt->fetch();

            if (!$existingField) {
                // Create custom field for storing service UUID
                $stmt = $pdo->prepare("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, fieldoptions, regexpr, adminonly, required, showorder, showinvoice) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute(['product', $productId, 'coolify_service_uuid', 'text', 'Coolify Service UUID', '', '', '1', '0', '0', '0']);
            }

            // Add custom field for deployment status
            $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = 'coolify_deployment_status'");
            $stmt->execute([$productId]);
            $existingStatusField = $stmt->fetch();

            if (!$existingStatusField) {
                $stmt = $pdo->prepare("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, fieldoptions, regexpr, adminonly, required, showorder, showinvoice) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute(['product', $productId, 'coolify_deployment_status', 'text', 'Deployment Status', '', '', '1', '0', '0', '0']);
            }

            // Add custom field for last deployment date
            $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = ? AND fieldname = 'coolify_last_deployment'");
            $stmt->execute([$productId]);
            $existingDateField = $stmt->fetch();

            if (!$existingDateField) {
                $stmt = $pdo->prepare("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, fieldoptions, regexpr, adminonly, required, showorder, showinvoice) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)");
                $stmt->execute(['product', $productId, 'coolify_last_deployment', 'text', 'Last Deployment Date', '', '', '1', '0', '0', '0']);
            }
        }
    } catch (Exception $e) {
        logModuleCall('coolify', 'setupCustomFields', [], $e->getMessage(), '');
    }
}

/**
 * Create additional database tables if needed
 */
function coolify_createDatabaseTables()
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Create table for storing deployment logs
        $createLogTable = "CREATE TABLE IF NOT EXISTS mod_coolify_deployment_logs (
            id int(11) NOT NULL AUTO_INCREMENT,
            service_id int(11) NOT NULL,
            action varchar(50) NOT NULL,
            status varchar(20) NOT NULL,
            message text,
            created_at timestamp DEFAULT CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            KEY service_id (service_id)
        )";

        $pdo->exec($createLogTable);

        // Create table for storing service configurations
        $createConfigTable = "CREATE TABLE IF NOT EXISTS mod_coolify_service_configs (
            id int(11) NOT NULL AUTO_INCREMENT,
            service_id int(11) NOT NULL,
            config_key varchar(100) NOT NULL,
            config_value text,
            updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
            PRIMARY KEY (id),
            UNIQUE KEY service_config (service_id, config_key)
        )";

        $pdo->exec($createConfigTable);
    } catch (Exception $e) {
        logModuleCall('coolify', 'createDatabaseTables', [], $e->getMessage(), '');
    }
}

/**
 * Post-creation setup tasks
 */
function coolify_postCreateSetup($vars)
{
    // Log the creation
    coolify_logDeployment($vars['serviceid'], 'create', 'initiated', 'Service creation initiated');
    
    // Update deployment status
    coolify_updateCustomField($vars['serviceid'], 'coolify_deployment_status', 'provisioning');
    coolify_updateCustomField($vars['serviceid'], 'coolify_last_deployment', date('Y-m-d H:i:s'));
}

/**
 * Post-termination cleanup tasks
 */
function coolify_postTerminateCleanup($vars)
{
    // Log the termination
    coolify_logDeployment($vars['serviceid'], 'terminate', 'completed', 'Service terminated successfully');
    
    // Clean up deployment logs (optional - keep for historical purposes)
    // Note: Deployment logs are kept for historical purposes
    
    // Clean up service configurations
    try {
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("DELETE FROM mod_coolify_service_configs WHERE service_id = ?");
        $stmt->execute([$vars['serviceid']]);
    } catch (Exception $e) {
        logModuleCall('coolify', 'postTerminateCleanup', $vars, $e->getMessage(), '');
    }
}

/**
 * Log deployment actions
 */
function coolify_logDeployment($serviceId, $action, $status, $message = '')
{
    try {
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("INSERT INTO mod_coolify_deployment_logs (service_id, action, status, message) VALUES (?, ?, ?, ?)");
        $stmt->execute([$serviceId, $action, $status, $message]);
    } catch (Exception $e) {
        logModuleCall('coolify', 'logDeployment', compact('serviceId', 'action', 'status', 'message'), $e->getMessage(), '');
    }
}

/**
 * Update custom field value
 */
function coolify_updateCustomField($serviceId, $fieldName, $value)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        // Get product ID for this service
        $stmt = $pdo->prepare("SELECT packageid FROM tblhosting WHERE id = ?");
        $stmt->execute([$serviceId]);
        $productRow = $stmt->fetch(PDO::FETCH_ASSOC);

        if ($productRow) {
            $productId = $productRow['packageid'];

            // Get custom field ID
            $stmt = $pdo->prepare("SELECT id FROM tblcustomfields WHERE fieldname = ? AND type = 'product' AND relid = ?");
            $stmt->execute([$fieldName, $productId]);
            $fieldRow = $stmt->fetch(PDO::FETCH_ASSOC);

            if ($fieldRow) {
                $fieldId = $fieldRow['id'];

                // Check if value already exists
                $stmt = $pdo->prepare("SELECT id FROM tblcustomfieldsvalues WHERE fieldid = ? AND relid = ?");
                $stmt->execute([$fieldId, $serviceId]);
                $existing = $stmt->fetch();

                if ($existing) {
                    // Update existing
                    $stmt = $pdo->prepare("UPDATE tblcustomfieldsvalues SET value = ? WHERE fieldid = ? AND relid = ?");
                    $stmt->execute([$value, $fieldId, $serviceId]);
                } else {
                    // Insert new
                    $stmt = $pdo->prepare("INSERT INTO tblcustomfieldsvalues (fieldid, relid, value) VALUES (?, ?, ?)");
                    $stmt->execute([$fieldId, $serviceId, $value]);
                }
            }
        }
    } catch (Exception $e) {
        logModuleCall('coolify', 'updateCustomField', compact('serviceId', 'fieldName', 'value'), $e->getMessage(), '');
    }
}

/**
 * Get deployment logs for a service
 */
function coolify_getDeploymentLogs($serviceId, $limit = 50)
{
    try {
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("SELECT * FROM mod_coolify_deployment_logs WHERE service_id = ? ORDER BY created_at DESC LIMIT ?");
        $stmt->execute([$serviceId, intval($limit)]);
        return $stmt->fetchAll(PDO::FETCH_ASSOC);
    } catch (Exception $e) {
        logModuleCall('coolify', 'getDeploymentLogs', compact('serviceId', 'limit'), $e->getMessage(), '');
        return [];
    }
}

/**
 * Store service configuration
 */
function coolify_setServiceConfig($serviceId, $key, $value)
{
    try {
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("INSERT INTO mod_coolify_service_configs (service_id, config_key, config_value) VALUES (?, ?, ?)
                               ON DUPLICATE KEY UPDATE config_value = ?");
        $stmt->execute([$serviceId, $key, $value, $value]);
    } catch (Exception $e) {
        logModuleCall('coolify', 'setServiceConfig', compact('serviceId', 'key', 'value'), $e->getMessage(), '');
    }
}

/**
 * Get service configuration
 */
function coolify_getServiceConfig($serviceId, $key = null)
{
    try {
        $pdo = Capsule::connection()->getPdo();

        if ($key) {
            $stmt = $pdo->prepare("SELECT config_value FROM mod_coolify_service_configs WHERE service_id = ? AND config_key = ?");
            $stmt->execute([$serviceId, $key]);
            $row = $stmt->fetch(PDO::FETCH_ASSOC);
            return $row ? $row['config_value'] : null;
        } else {
            $stmt = $pdo->prepare("SELECT config_key, config_value FROM mod_coolify_service_configs WHERE service_id = ?");
            $stmt->execute([$serviceId]);
            $configs = [];
            while ($row = $stmt->fetch(PDO::FETCH_ASSOC)) {
                $configs[$row['config_key']] = $row['config_value'];
            }
            return $configs;
        }
    } catch (Exception $e) {
        logModuleCall('coolify', 'getServiceConfig', compact('serviceId', 'key'), $e->getMessage(), '');
        return $key ? null : [];
    }
}

/**
 * Clean up custom fields (optional - for module deactivation)
 */
function coolify_cleanupCustomFields()
{
    try {
        $pdo = Capsule::connection()->getPdo();
        // Remove custom fields (be careful with this in production!)
        $stmt = $pdo->prepare("DELETE FROM tblcustomfields WHERE fieldname IN ('coolify_service_uuid', 'coolify_deployment_status', 'coolify_last_deployment')");
        $stmt->execute();
        $stmt = $pdo->prepare("DELETE FROM tblcustomfieldsvalues WHERE fieldid NOT IN (SELECT id FROM tblcustomfields)");
        $stmt->execute();
    } catch (Exception $e) {
        logModuleCall('coolify', 'cleanupCustomFields', [], $e->getMessage(), '');
    }
}

/**
 * Admin area custom button array (include admin functions)
 */
add_hook('AdminAreaPage', 1, function($vars) {
    if ($vars['filename'] == 'clientsservices' && isset($_GET['id'])) {
        // Include admin functions for custom buttons
        require_once ROOTDIR . '/modules/servers/coolify/lib/AdminFunctions.php';
        
        // Handle POST requests for configuration updates
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
            $serviceId = $_GET['id'];

            try {
                $pdo = Capsule::connection()->getPdo();
                $stmt = $pdo->prepare("SELECT * FROM tblhosting WHERE id = ?");
                $stmt->execute([$serviceId]);
                $serviceData = $stmt->fetch(PDO::FETCH_ASSOC);

                if ($serviceData && $serviceData['server'] && $_POST['action'] == 'updateServiceConfig') {
                    $stmt = $pdo->prepare("SELECT * FROM tblservers WHERE id = ?");
                    $stmt->execute([$serviceData['server']]);
                    $serverData = $stmt->fetch(PDO::FETCH_ASSOC);

                    $stmt = $pdo->prepare("SELECT * FROM tblproducts WHERE id = ?");
                    $stmt->execute([$serviceData['packageid']]);
                    $productData = $stmt->fetch(PDO::FETCH_ASSOC);

                    if ($serverData && $productData && $productData['servertype'] == 'coolify') {
                        // Prepare params array similar to module functions
                        $params = array(
                            'serviceid' => $serviceId,
                            'configoption1' => $serverData['accesshash'], // API Token stored in access hash
                            // Add other config options as needed
                        );

                        $result = coolify_handleConfigUpdate($params);
                        // Display result or redirect
                    }
                }
            } catch (Exception $e) {
                logModuleCall('coolify', 'AdminAreaPage', ['serviceId' => $serviceId], $e->getMessage(), '');
            }
        }
    }
}); 
<?php
/**
 * Test script to verify Coolify plugin functionality
 * This script helps diagnose issues with the WHMCS Coolify plugin
 */

// Simulate WHMCS environment
define("WHMCS", true);

// Mock the Capsule class for testing
if (!class_exists('Illuminate\Database\Capsule\Manager')) {
    class MockCapsule {
        public static function connection() {
            return new MockConnection();
        }
    }
    
    class MockConnection {
        public function getPdo() {
            return new MockPDO();
        }
    }
    
    class MockPDO {
        public function prepare($sql) {
            echo "SQL: " . $sql . "\n";
            return new MockStatement();
        }
        
        public function exec($sql) {
            echo "EXEC: " . $sql . "\n";
            return true;
        }
    }
    
    class MockStatement {
        public function execute($params = []) {
            echo "PARAMS: " . json_encode($params) . "\n";
            return true;
        }
        
        public function fetch($mode = null) {
            return ['id' => 1, 'value' => 'test', 'packageid' => 1];
        }
        
        public function fetchAll($mode = null) {
            return [['id' => 1, 'value' => 'test']];
        }
    }
    
    // Create alias for the mock
    class_alias('MockCapsule', 'Illuminate\Database\Capsule\Manager');
}

// Mock logModuleCall function
if (!function_exists('logModuleCall')) {
    function logModuleCall($module, $action, $request, $response, $error = '', $hidden = []) {
        echo "LOG: $module - $action\n";
        if ($error) {
            echo "ERROR: $error\n";
        }
    }
}

// Mock WHMCS hook function
if (!function_exists('add_hook')) {
    function add_hook($hookPoint, $priority, $function) {
        echo "HOOK: $hookPoint registered\n";
    }
}

echo "=== Testing Coolify Plugin ===\n\n";

try {
    // Test loading the main module file
    echo "1. Loading main module file...\n";
    require_once 'modules/servers/coolify/coolify.php';
    echo "✓ Main module loaded successfully\n\n";
    
    // Test metadata function
    echo "2. Testing metadata function...\n";
    $metadata = coolify_MetaData();
    echo "✓ Metadata: " . json_encode($metadata) . "\n\n";
    
    // Test config options
    echo "3. Testing config options...\n";
    $configOptions = coolify_ConfigOptions();
    echo "✓ Config options loaded: " . count($configOptions) . " options\n\n";
    
    // Test helper functions
    echo "4. Testing helper functions...\n";
    $uuid = getServiceUuid(123);
    echo "✓ getServiceUuid function works\n";
    
    $password = generateRandomPassword();
    echo "✓ Generated password: " . substr($password, 0, 4) . "...\n";
    
    $dockerCompose = generateN8nDockerCompose('test.example.com', '512', 'n8nio/n8n:latest', 'testpass');
    echo "✓ Docker compose generated (" . strlen($dockerCompose) . " chars)\n\n";
    
    // Test hooks file
    echo "5. Loading hooks file...\n";
    require_once 'modules/servers/coolify/hooks.php';
    echo "✓ Hooks loaded successfully\n\n";
    
    // Test admin functions
    echo "6. Loading admin functions...\n";
    require_once 'modules/servers/coolify/lib/AdminFunctions.php';
    echo "✓ Admin functions loaded successfully\n\n";
    
    echo "=== All Tests Passed! ===\n";
    echo "The plugin appears to be syntactically correct and should load in WHMCS.\n\n";
    
    echo "Next steps:\n";
    echo "1. Upload the plugin to your WHMCS modules/servers/ directory\n";
    echo "2. Create a server in WHMCS admin with type 'Coolify n8n'\n";
    echo "3. Configure the server with your Coolify API token\n";
    echo "4. Create a product using the Coolify server module\n";
    echo "5. Test creating a service\n";
    
} catch (Exception $e) {
    echo "❌ Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
} catch (Error $e) {
    echo "❌ Fatal Error: " . $e->getMessage() . "\n";
    echo "File: " . $e->getFile() . "\n";
    echo "Line: " . $e->getLine() . "\n";
}

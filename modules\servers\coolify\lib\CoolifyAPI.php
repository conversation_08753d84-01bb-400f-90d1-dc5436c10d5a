<?php
/**
 * Coolify API Wrapper Class
 *
 * This class handles all communication with the Coolify API
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

class CoolifyAPI
{
    private $apiToken;
    private $baseUrl;
    
    public function __construct($apiToken, $baseUrl = 'https://app.coolify.io/api/v1')
    {
        $this->apiToken = $apiToken;
        $this->baseUrl = rtrim($baseUrl, '/');
    }
    
    /**
     * Make HTTP request to Coolify API
     */
    private function makeRequest($endpoint, $method = 'GET', $data = null)
    {
        $url = $this->baseUrl . '/' . ltrim($endpoint, '/');
        
        $headers = array(
            'Authorization: Bearer ' . $this->apiToken,
            'Content-Type: application/json',
            'Accept: application/json'
        );
        
        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $url,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTPHEADER => $headers,
            CURLOPT_CUSTOMREQUEST => $method
        ));
        
        if ($data && in_array($method, array('POST', 'PUT', 'PATCH'))) {
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
        }
        
        $response = curl_exec($ch);
        $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $error = curl_error($ch);
        curl_close($ch);
        
        if ($error) {
            throw new Exception('cURL Error: ' . $error);
        }
        
        $decodedResponse = json_decode($response, true);
        
        if ($httpCode >= 400) {
            $errorMessage = 'HTTP ' . $httpCode;

            // Enhanced error reporting for different HTTP codes
            if ($httpCode === 422) {
                $errorMessage .= ' (Validation Error)';
                if (isset($decodedResponse['errors'])) {
                    $errorMessage .= ': ' . json_encode($decodedResponse['errors']);
                } elseif (isset($decodedResponse['message'])) {
                    $errorMessage .= ': ' . $decodedResponse['message'];
                }
            } elseif ($httpCode === 401) {
                $errorMessage .= ' (Unauthorized): Invalid API token or insufficient permissions';
            } elseif ($httpCode === 404) {
                $errorMessage .= ' (Not Found): Resource does not exist';
            } else {
                if (isset($decodedResponse['message'])) {
                    $errorMessage .= ': ' . $decodedResponse['message'];
                } elseif (isset($decodedResponse['error'])) {
                    $errorMessage .= ': ' . $decodedResponse['error'];
                } elseif ($decodedResponse) {
                    $errorMessage .= ': ' . json_encode($decodedResponse);
                } else {
                    $errorMessage .= ': ' . $response;
                }
            }

            throw new Exception($errorMessage);
        }
        
        return $decodedResponse;
    }
    
    /**
     * Get all servers
     */
    public function getServers()
    {
        return $this->makeRequest('/servers');
    }
    
    /**
     * Get server by UUID
     */
    public function getServer($uuid)
    {
        return $this->makeRequest('/servers/' . $uuid);
    }
    
    /**
     * Create a new server
     */
    public function createServer($data)
    {
        return $this->makeRequest('/servers', 'POST', $data);
    }
    
    /**
     * Get resources by server UUID
     */
    public function getServerResources($serverUuid)
    {
        return $this->makeRequest('/servers/' . $serverUuid . '/resources');
    }
    
    /**
     * Create a new service
     */
    public function createService($data)
    {
        // Validate required fields before sending
        $requiredFields = ['name', 'project_uuid', 'environment_name', 'server_uuid', 'destination_uuid'];
        foreach ($requiredFields as $field) {
            if (empty($data[$field])) {
                throw new Exception("Missing required field: $field");
            }
        }

        // Prepare service data with proper structure for Coolify API
        // Note: Removed 'type' field as it's not allowed in current API
        $serviceData = [
            'name' => $data['name'],
            'description' => isset($data['description']) ? $data['description'] : '',
            'project_uuid' => $data['project_uuid'],
            'environment_name' => $data['environment_name'],
            'server_uuid' => $data['server_uuid'],
            'destination_uuid' => $data['destination_uuid'],
            'docker_compose_raw' => $data['docker_compose_raw'],
            'instant_deploy' => isset($data['instant_deploy']) ? $data['instant_deploy'] : false
        ];

        return $this->makeRequest('/services', 'POST', $serviceData);
    }
    
    /**
     * Get service details
     */
    public function getService($uuid)
    {
        return $this->makeRequest('/services/' . $uuid);
    }
    
    /**
     * Delete a service
     */
    public function deleteService($uuid)
    {
        return $this->makeRequest('/services/' . $uuid, 'DELETE');
    }
    
    /**
     * Start a service
     */
    public function startService($uuid)
    {
        return $this->makeRequest('/services/' . $uuid . '/start', 'POST');
    }
    
    /**
     * Stop a service
     */
    public function stopService($uuid)
    {
        return $this->makeRequest('/services/' . $uuid . '/stop', 'POST');
    }
    
    /**
     * Restart a service
     */
    public function restartService($uuid)
    {
        return $this->makeRequest('/services/' . $uuid . '/restart', 'POST');
    }
    
    /**
     * Get service logs
     */
    public function getServiceLogs($uuid, $lines = 100)
    {
        return $this->makeRequest('/services/' . $uuid . '/logs?lines=' . $lines);
    }
    
    /**
     * Update service configuration
     */
    public function updateService($uuid, $data)
    {
        return $this->makeRequest('/services/' . $uuid, 'PATCH', $data);
    }
    
    /**
     * Deploy n8n service with specific configuration
     */
    public function deployN8nService($config)
    {
        // Prepare n8n-specific Docker configuration
        // Note: Removed 'type' field as it's not allowed in current Coolify API
        $n8nConfig = array(
            'name' => $config['name'],
            'description' => $config['description'],
            'project_uuid' => $config['project_uuid'],
            'environment_name' => $config['environment_name'],
            'server_uuid' => $config['server_uuid'],
            'destination_uuid' => $config['destination_uuid'],
            'instant_deploy' => true,
            'docker_compose_raw' => $this->generateN8nDockerCompose($config)
        );
        
        return $this->createService($n8nConfig);
    }
    
    /**
     * Generate Docker Compose configuration for n8n
     */
    private function generateN8nDockerCompose($config)
    {
        $domain = isset($config['domain']) ? $config['domain'] : 'n8n.example.com';
        $memoryLimit = isset($config['memory_limit']) ? $config['memory_limit'] . 'm' : '512m';
        $image = isset($config['n8n_image']) ? $config['n8n_image'] : 'n8nio/n8n:latest';
        
        $dockerCompose = "version: '3.8'

services:
  n8n:
    image: {$image}
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=\${N8N_PASSWORD}
      - N8N_HOST={$domain}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - NODE_ENV=production
      - WEBHOOK_URL=https://{$domain}/
      - GENERIC_TIMEZONE=UTC
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - coolify
    deploy:
      resources:
        limits:
          memory: {$memoryLimit}
    labels:
      - coolify.managed=true
      - traefik.enable=true
      - traefik.http.routers.n8n.rule=Host(\`{$domain}\`)
      - traefik.http.routers.n8n.tls=true
      - traefik.http.routers.n8n.tls.certresolver=letsencrypt
      - traefik.http.services.n8n.loadbalancer.server.port=5678

volumes:
  n8n_data:
    driver: local

networks:
  coolify:
    external: true";

        return $dockerCompose;
    }
    
    /**
     * Get service status
     */
    public function getServiceStatus($uuid)
    {
        try {
            $service = $this->getService($uuid);
            return isset($service['status']) ? $service['status'] : 'unknown';
        } catch (Exception $e) {
            return 'error';
        }
    }
    
    /**
     * Test API connection
     */
    public function testConnection()
    {
        try {
            $servers = $this->getServers();
            return array(
                'success' => true,
                'message' => 'Connected successfully',
                'data' => $servers
            );
        } catch (Exception $e) {
            return array(
                'success' => false,
                'message' => $e->getMessage(),
                'data' => null
            );
        }
    }
    
    /**
     * Create environment variables for n8n service
     */
    public function setServiceEnvironmentVariables($uuid, $variables)
    {
        return $this->makeRequest('/services/' . $uuid . '/environment-variables', 'POST', $variables);
    }
    
    /**
     * Get projects
     */
    public function getProjects()
    {
        return $this->makeRequest('/projects');
    }
    
    /**
     * Get environments for a project
     */
    public function getProjectEnvironments($projectUuid)
    {
        return $this->makeRequest('/projects/' . $projectUuid . '/environments');
    }
    
    /**
     * Generate unique subdomain
     */
    public function generateUniqueSubdomain($username, $baseDomain)
    {
        $subdomain = strtolower(preg_replace('/[^a-zA-Z0-9]/', '', $username));
        if (empty($subdomain)) {
            $subdomain = 'user' . time();
        }
        
        return $subdomain . '.' . $baseDomain;
    }
} 
<?php
/**
 * WHMCS Coolify Module Hooks
 *
 * This file handles module setup, custom field creation, and other initialization tasks
 */

add_hook('ModuleActivate', 1, function($vars) {
    if ($vars['module'] == 'coolify') {
        coolify_setupCustomFields();
        coolify_createDatabaseTables();
    }
});

add_hook('ModuleDeactivate', 1, function($vars) {
    if ($vars['module'] == 'coolify') {
        // Optionally clean up when module is deactivated
        // coolify_cleanupCustomFields();
    }
});

add_hook('PreModuleCreate', 1, function($vars) {
    if ($vars['producttype'] == 'server' && $vars['servertype'] == 'coolify') {
        // Pre-creation hook - can be used for validation
        return $vars;
    }
});

add_hook('AfterModuleCreate', 1, function($vars) {
    if ($vars['producttype'] == 'server' && $vars['servertype'] == 'coolify') {
        // Post-creation hook - can be used for additional setup
        coolify_postCreateSetup($vars);
    }
});

add_hook('AfterModuleTerminate', 1, function($vars) {
    if ($vars['producttype'] == 'server' && $vars['servertype'] == 'coolify') {
        // Clean up after termination
        coolify_postTerminateCleanup($vars);
    }
});

/**
 * Setup custom fields required for the module
 */
function coolify_setupCustomFields()
{
    // Get all products that use coolify server module
    $result = mysql_query("SELECT id FROM tblproducts WHERE servertype = 'coolify'");
    
    while ($product = mysql_fetch_assoc($result)) {
        $productId = $product['id'];
        
        // Check if custom field already exists
        $existingField = mysql_query("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = '$productId' AND fieldname = 'coolify_service_uuid'");
        
        if (mysql_num_rows($existingField) == 0) {
            // Create custom field for storing service UUID
            mysql_query("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, fieldoptions, regexpr, adminonly, required, showorder, showinvoice) VALUES (
                'product',
                '$productId',
                'coolify_service_uuid',
                'text',
                'Coolify Service UUID',
                '',
                '',
                '1',
                '0',
                '0',
                '0'
            )");
        }
        
        // Add custom field for deployment status
        $existingStatusField = mysql_query("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = '$productId' AND fieldname = 'coolify_deployment_status'");
        
        if (mysql_num_rows($existingStatusField) == 0) {
            mysql_query("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, fieldoptions, regexpr, adminonly, required, showorder, showinvoice) VALUES (
                'product',
                '$productId',
                'coolify_deployment_status',
                'text',
                'Deployment Status',
                '',
                '',
                '1',
                '0',
                '0',
                '0'
            )");
        }
        
        // Add custom field for last deployment date
        $existingDateField = mysql_query("SELECT id FROM tblcustomfields WHERE type = 'product' AND relid = '$productId' AND fieldname = 'coolify_last_deployment'");
        
        if (mysql_num_rows($existingDateField) == 0) {
            mysql_query("INSERT INTO tblcustomfields (type, relid, fieldname, fieldtype, description, fieldoptions, regexpr, adminonly, required, showorder, showinvoice) VALUES (
                'product',
                '$productId',
                'coolify_last_deployment',
                'text',
                'Last Deployment Date',
                '',
                '',
                '1',
                '0',
                '0',
                '0'
            )");
        }
    }
}

/**
 * Create additional database tables if needed
 */
function coolify_createDatabaseTables()
{
    // Create table for storing deployment logs
    $createLogTable = "CREATE TABLE IF NOT EXISTS mod_coolify_deployment_logs (
        id int(11) NOT NULL AUTO_INCREMENT,
        service_id int(11) NOT NULL,
        action varchar(50) NOT NULL,
        status varchar(20) NOT NULL,
        message text,
        created_at timestamp DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        KEY service_id (service_id)
    )";
    
    mysql_query($createLogTable);
    
    // Create table for storing service configurations
    $createConfigTable = "CREATE TABLE IF NOT EXISTS mod_coolify_service_configs (
        id int(11) NOT NULL AUTO_INCREMENT,
        service_id int(11) NOT NULL,
        config_key varchar(100) NOT NULL,
        config_value text,
        updated_at timestamp DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
        PRIMARY KEY (id),
        UNIQUE KEY service_config (service_id, config_key)
    )";
    
    mysql_query($createConfigTable);
}

/**
 * Post-creation setup tasks
 */
function coolify_postCreateSetup($vars)
{
    // Log the creation
    coolify_logDeployment($vars['serviceid'], 'create', 'initiated', 'Service creation initiated');
    
    // Update deployment status
    coolify_updateCustomField($vars['serviceid'], 'coolify_deployment_status', 'provisioning');
    coolify_updateCustomField($vars['serviceid'], 'coolify_last_deployment', date('Y-m-d H:i:s'));
}

/**
 * Post-termination cleanup tasks
 */
function coolify_postTerminateCleanup($vars)
{
    // Log the termination
    coolify_logDeployment($vars['serviceid'], 'terminate', 'completed', 'Service terminated successfully');
    
    // Clean up deployment logs (optional - keep for historical purposes)
    // mysql_query("DELETE FROM mod_coolify_deployment_logs WHERE service_id = '" . $vars['serviceid'] . "'");
    
    // Clean up service configurations
    mysql_query("DELETE FROM mod_coolify_service_configs WHERE service_id = '" . $vars['serviceid'] . "'");
}

/**
 * Log deployment actions
 */
function coolify_logDeployment($serviceId, $action, $status, $message = '')
{
    $serviceId = mysql_real_escape_string($serviceId);
    $action = mysql_real_escape_string($action);
    $status = mysql_real_escape_string($status);
    $message = mysql_real_escape_string($message);
    
    mysql_query("INSERT INTO mod_coolify_deployment_logs (service_id, action, status, message) VALUES ('$serviceId', '$action', '$status', '$message')");
}

/**
 * Update custom field value
 */
function coolify_updateCustomField($serviceId, $fieldName, $value)
{
    $serviceId = mysql_real_escape_string($serviceId);
    $fieldName = mysql_real_escape_string($fieldName);
    $value = mysql_real_escape_string($value);
    
    // Get product ID for this service
    $productResult = mysql_query("SELECT packageid FROM tblhosting WHERE id = '$serviceId'");
    if ($productRow = mysql_fetch_assoc($productResult)) {
        $productId = $productRow['packageid'];
        
        // Get custom field ID
        $fieldResult = mysql_query("SELECT id FROM tblcustomfields WHERE fieldname = '$fieldName' AND type = 'product' AND relid = '$productId'");
        if ($fieldRow = mysql_fetch_assoc($fieldResult)) {
            $fieldId = $fieldRow['id'];
            
            // Check if value already exists
            $existingResult = mysql_query("SELECT id FROM tblcustomfieldsvalues WHERE fieldid = '$fieldId' AND relid = '$serviceId'");
            if (mysql_num_rows($existingResult) > 0) {
                // Update existing
                mysql_query("UPDATE tblcustomfieldsvalues SET value = '$value' WHERE fieldid = '$fieldId' AND relid = '$serviceId'");
            } else {
                // Insert new
                mysql_query("INSERT INTO tblcustomfieldsvalues (fieldid, relid, value) VALUES ('$fieldId', '$serviceId', '$value')");
            }
        }
    }
}

/**
 * Get deployment logs for a service
 */
function coolify_getDeploymentLogs($serviceId, $limit = 50)
{
    $serviceId = mysql_real_escape_string($serviceId);
    $limit = intval($limit);
    
    $result = mysql_query("SELECT * FROM mod_coolify_deployment_logs WHERE service_id = '$serviceId' ORDER BY created_at DESC LIMIT $limit");
    
    $logs = array();
    while ($row = mysql_fetch_assoc($result)) {
        $logs[] = $row;
    }
    
    return $logs;
}

/**
 * Store service configuration
 */
function coolify_setServiceConfig($serviceId, $key, $value)
{
    $serviceId = mysql_real_escape_string($serviceId);
    $key = mysql_real_escape_string($key);
    $value = mysql_real_escape_string($value);
    
    mysql_query("INSERT INTO mod_coolify_service_configs (service_id, config_key, config_value) VALUES ('$serviceId', '$key', '$value') 
                 ON DUPLICATE KEY UPDATE config_value = '$value'");
}

/**
 * Get service configuration
 */
function coolify_getServiceConfig($serviceId, $key = null)
{
    $serviceId = mysql_real_escape_string($serviceId);
    
    if ($key) {
        $key = mysql_real_escape_string($key);
        $result = mysql_query("SELECT config_value FROM mod_coolify_service_configs WHERE service_id = '$serviceId' AND config_key = '$key'");
        if ($row = mysql_fetch_assoc($result)) {
            return $row['config_value'];
        }
        return null;
    } else {
        $result = mysql_query("SELECT config_key, config_value FROM mod_coolify_service_configs WHERE service_id = '$serviceId'");
        $configs = array();
        while ($row = mysql_fetch_assoc($result)) {
            $configs[$row['config_key']] = $row['config_value'];
        }
        return $configs;
    }
}

/**
 * Clean up custom fields (optional - for module deactivation)
 */
function coolify_cleanupCustomFields()
{
    // Remove custom fields (be careful with this in production!)
    mysql_query("DELETE FROM tblcustomfields WHERE fieldname IN ('coolify_service_uuid', 'coolify_deployment_status', 'coolify_last_deployment')");
    mysql_query("DELETE FROM tblcustomfieldsvalues WHERE fieldid NOT IN (SELECT id FROM tblcustomfields)");
}

/**
 * Admin area custom button array (include admin functions)
 */
add_hook('AdminAreaPage', 1, function($vars) {
    if ($vars['filename'] == 'clientsservices' && isset($_GET['id'])) {
        // Include admin functions for custom buttons
        require_once ROOTDIR . '/modules/servers/coolify/lib/AdminFunctions.php';
        
        // Handle POST requests for configuration updates
        if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['action'])) {
            $serviceId = $_GET['id'];
            $serviceData = mysql_fetch_assoc(mysql_query("SELECT * FROM tblhosting WHERE id = '$serviceId'"));
            
            if ($serviceData && $serviceData['server'] && $_POST['action'] == 'updateServiceConfig') {
                $serverData = mysql_fetch_assoc(mysql_query("SELECT * FROM tblservers WHERE id = '" . $serviceData['server'] . "'"));
                $productData = mysql_fetch_assoc(mysql_query("SELECT * FROM tblproducts WHERE id = '" . $serviceData['packageid'] . "'"));
                
                if ($serverData && $productData && $productData['servertype'] == 'coolify') {
                    // Prepare params array similar to module functions
                    $params = array(
                        'serviceid' => $serviceId,
                        'configoption1' => $serverData['accesshash'], // API Token stored in access hash
                        // Add other config options as needed
                    );
                    
                    $result = coolify_handleConfigUpdate($params);
                    // Display result or redirect
                }
            }
        }
    }
}); 
<?php
/**
 * WHMCS Coolify Server Module
 *
 * This module integrates WHMCS with Coolify to automatically deploy n8n instances
 *
 * <AUTHOR> Name
 * @version 1.0
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

use Illuminate\Database\Capsule\Manager as Capsule;

require_once __DIR__ . '/lib/CoolifyAPI.php';

/**
 * Define module related meta data.
 */
function coolify_MetaData()
{
    return array(
        'DisplayName' => 'Coolify n8n',
        'APIVersion' => '1.1',
        'RequiresServer' => true,
        'DefaultNonSSLPort' => '80',
        'DefaultSSLPort' => '443',
        'ServiceSingleSignOnLabel' => 'Open n8n Dashboard',
    );
}

/**
 * Define product configuration options.
 */
function coolify_ConfigOptions()
{
    return array(
        'api_token' => array(
            'FriendlyName' => 'Coolify API Token',
            'Type' => 'password',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Enter your Coolify API Bearer Token',
        ),
        'project_uuid' => array(
            'FriendlyName' => 'Project UUID',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Coolify Project UUID where services will be deployed',
        ),
        'environment_name' => array(
            'FriendlyName' => 'Environment Name',
            'Type' => 'text',
            'Size' => '25',
            'Default' => 'production',
            'Description' => 'Environment name for deployments',
        ),
        'server_uuid' => array(
            'FriendlyName' => 'Server UUID',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Coolify Server UUID where n8n will be deployed',
        ),
        'destination_uuid' => array(
            'FriendlyName' => 'Destination UUID',
            'Type' => 'text',
            'Size' => '50',
            'Default' => '',
            'Description' => 'Destination UUID for the deployment',
        ),
        'base_domain' => array(
            'FriendlyName' => 'Base Domain',
            'Type' => 'text',
            'Size' => '30',
            'Default' => 'yourdomain.com',
            'Description' => 'Base domain for n8n instances (e.g., customers.yourdomain.com)',
        ),
        'n8n_image' => array(
            'FriendlyName' => 'n8n Docker Image',
            'Type' => 'text',
            'Size' => '30',
            'Default' => 'n8nio/n8n:latest',
            'Description' => 'Docker image to use for n8n deployments',
        ),
        'memory_limit' => array(
            'FriendlyName' => 'Memory Limit (MB)',
            'Type' => 'text',
            'Size' => '10',
            'Default' => '512',
            'Description' => 'Memory limit for n8n container in MB',
        ),
        'coolify_url' => array(
            'FriendlyName' => 'Coolify Instance URL',
            'Type' => 'text',
            'Size' => '50',
            'Default' => 'https://app.coolify.io',
            'Description' => 'Your Coolify instance URL (e.g., https://coolify.yourdomain.com) - without /api/v1',
        ),
    );
}

/**
 * Provision a new instance of n8n.
 */
function coolify_CreateAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }

        // Validate required configuration options
        $requiredConfigs = [
            'configoption2' => 'Project UUID',
            'configoption3' => 'Environment Name',
            'configoption4' => 'Server UUID',
            'configoption5' => 'Destination UUID',
            'configoption6' => 'Base Domain'
        ];

        foreach ($requiredConfigs as $configKey => $configName) {
            if (empty($params[$configKey])) {
                return "Error: Missing required configuration: $configName. Please configure the server settings.";
            }
        }

        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';

        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        // Generate unique service name
        $serviceName = 'n8n-' . strtolower($params['username']) . '-' . time();
        $subdomain = strtolower($params['username']);
        $domain = $subdomain . '.' . $params['configoption6']; // Base domain
        
        // Generate n8n password
        $n8nPassword = generateRandomPassword();
        
        // Generate n8n Docker Compose configuration
        $dockerCompose = generateN8nDockerCompose($domain, $params['configoption8'], $params['configoption7'], $n8nPassword);
        
        // Prepare service configuration with Docker Compose
        $serviceConfig = array(
            'name' => $serviceName,
            'description' => 'n8n instance for ' . $params['clientsdetails']['firstname'] . ' ' . $params['clientsdetails']['lastname'],
            'project_uuid' => $params['configoption2'],
            'environment_name' => $params['configoption3'],
            'server_uuid' => $params['configoption4'],
            'destination_uuid' => $params['configoption5'],
            'docker_compose_raw' => $dockerCompose,
            'instant_deploy' => true
        );
        
        // Log the service configuration for debugging
        logModuleCall(
            'coolify',
            'CreateAccount_Request',
            $serviceConfig,
            'Sending service creation request',
            '',
            array($apiToken) // Hide API token from logs
        );

        // Create the service
        $result = $api->createService($serviceConfig);

        if ($result && isset($result['uuid'])) {
            // Store service details in WHMCS
            $serviceUuid = $result['uuid'];

            // Update the service hostname and other details in WHMCS
            $pdo = Capsule::connection()->getPdo();
            $stmt = $pdo->prepare("UPDATE tblhosting SET domain = ?, username = ?, password = ? WHERE id = ?");
            $stmt->execute([$domain, 'admin', $n8nPassword, $params['serviceid']]);

            // Store additional data in custom fields or notes
            logModuleCall(
                'coolify',
                'CreateAccount_Success',
                $serviceConfig,
                $result,
                '',
                array($apiToken) // Hide API token from logs
            );

            // Store the service UUID for future reference
            $stmt = $pdo->prepare("INSERT INTO tblcustomfieldsvalues (fieldid, relid, value) VALUES (
                (SELECT id FROM tblcustomfields WHERE fieldname = 'coolify_service_uuid' AND type = 'product' AND relid = ? LIMIT 1),
                ?, ?
            )");
            $stmt->execute([$params['pid'], $params['serviceid'], $serviceUuid]);

            return 'success';
        } else {
            // Enhanced error reporting
            $errorMessage = 'Failed to create service';
            if (isset($result['message'])) {
                $errorMessage .= ': ' . $result['message'];
            } elseif (isset($result['errors'])) {
                $errorMessage .= ': ' . json_encode($result['errors']);
            } elseif (is_array($result)) {
                $errorMessage .= ': ' . json_encode($result);
            } else {
                $errorMessage .= ': Unknown error';
            }

            // Log the error for debugging
            logModuleCall(
                'coolify',
                'CreateAccount_Error',
                $serviceConfig,
                $result,
                $errorMessage,
                array($apiToken)
            );

            return $errorMessage;
        }
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'CreateAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Suspend an instance.
 */
function coolify_SuspendAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->stopService($serviceUuid);
        
        logModuleCall(
            'coolify',
            'SuspendAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'SuspendAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Unsuspend an instance.
 */
function coolify_UnsuspendAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->startService($serviceUuid);
        
        logModuleCall(
            'coolify',
            'UnsuspendAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'UnsuspendAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Terminate an instance.
 */
function coolify_TerminateAccount($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'Error: No API token configured. Please set the Coolify API token in the server Access Hash field.';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        
        $serviceUuid = getServiceUuid($params['serviceid']);
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->deleteService($serviceUuid);
        
        // Clean up custom field data
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("DELETE FROM tblcustomfieldsvalues WHERE relid = ? AND fieldid = (
            SELECT id FROM tblcustomfields WHERE fieldname = 'coolify_service_uuid' AND type = 'product' AND relid = ? LIMIT 1
        )");
        $stmt->execute([$params['serviceid'], $params['pid']]);
        
        logModuleCall(
            'coolify',
            'TerminateAccount',
            $params,
            $result,
            '',
            array($apiToken)
        );
        
        return 'success';
        
    } catch (Exception $e) {
        logModuleCall(
            'coolify',
            'TerminateAccount',
            $params,
            $e->getMessage(),
            $e->getTraceAsString()
        );
        
        return 'Error: ' . $e->getMessage();
    }
}

/**
 * Test connection to Coolify API.
 */
function coolify_TestConnection($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return array(
                'success' => false,
                'error' => 'No API token configured. Please set the Coolify API token in the server Access Hash field.',
            );
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $result = $api->getServers();
        
        if ($result && is_array($result)) {
            $success = true;
            $errorMsg = 'Connection successful. Found ' . count($result) . ' server(s).';
        } else {
            $success = false;
            $errorMsg = 'Failed to connect or retrieve servers.';
        }
        
    } catch (Exception $e) {
        $success = false;
        $errorMsg = 'Connection failed: ' . $e->getMessage();
    }
    
    return array(
        'success' => $success,
        'error' => $errorMsg,
    );
}

/**
 * Admin area output.
 */
function coolify_AdminCustomButtonArray($params)
{
    return array(
        'View n8n Service' => 'viewService',
        'Restart n8n' => 'restartService',
        'View Service Logs' => 'viewLogs',
    );
}

/**
 * Client area output.
 */
function coolify_ClientArea($params)
{
    $serviceUuid = getServiceUuid($params['serviceid']);
    
    if (!$serviceUuid) {
        return '<div class="alert alert-warning">n8n service not yet provisioned. Please wait while your instance is being set up.</div>';
    }
    
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return '<div class="alert alert-danger">API token not configured. Please contact support.</div>';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceInfo = $api->getService($serviceUuid);
        
        $output = '<div class="panel panel-default">';
        $output .= '<div class="panel-heading"><h3 class="panel-title"><i class="fa fa-cogs"></i> Your n8n Workflow Automation</h3></div>';
        $output .= '<div class="panel-body">';
        
        if ($serviceInfo) {
            $status = isset($serviceInfo['status']) ? $serviceInfo['status'] : 'unknown';
            $statusClass = ($status == 'running') ? 'success' : (($status == 'stopped') ? 'danger' : 'warning');
            $statusText = ($status == 'running') ? 'Online' : (($status == 'stopped') ? 'Offline' : ucfirst($status));
            
            $output .= '<div class="row">';
            $output .= '<div class="col-md-8">';
            $output .= '<h4>Service Information</h4>';
            $output .= '<p><strong>Status:</strong> <span class="label label-' . $statusClass . '">' . $statusText . '</span></p>';
            $output .= '<p><strong>Access URL:</strong> <a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-external-link"></i> Open n8n Dashboard</a></p>';
            $output .= '<p><strong>Domain:</strong> ' . htmlspecialchars($params['domain']) . '</p>';
            $output .= '</div>';
            $output .= '<div class="col-md-4">';
            $output .= '<h4>Quick Actions</h4>';
            if ($status == 'running') {
                $output .= '<p><a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-success btn-block">Launch n8n</a></p>';
            } else {
                $output .= '<p><span class="btn btn-default btn-block disabled">Service Unavailable</span></p>';
            }
            $output .= '</div>';
            $output .= '</div>';
            
            $output .= '<hr>';
            $output .= '<h4>About n8n</h4>';
            $output .= '<p>n8n is a powerful workflow automation tool that lets you connect different services and automate tasks. ';
            $output .= 'Access your dedicated n8n instance using the link above.</p>';
            
            if ($status == 'running') {
                $output .= '<div class="alert alert-info">';
                $output .= '<strong>Getting Started:</strong> Click "Launch n8n" to access your workflow automation dashboard. ';
                $output .= 'You can create workflows, connect to various services, and automate your business processes.';
                $output .= '</div>';
            } else {
                $output .= '<div class="alert alert-warning">';
                $output .= '<strong>Service Status:</strong> Your n8n instance is currently ' . strtolower($statusText) . '. ';
                $output .= 'If this persists, please contact support.';
                $output .= '</div>';
            }
        } else {
            $output .= '<div class="alert alert-warning">';
            $output .= '<strong>Service Information Unavailable</strong><br>';
            $output .= 'Unable to retrieve current service status. Your n8n instance may still be starting up.';
            $output .= '</div>';
        }
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
        
    } catch (Exception $e) {
        $output = '<div class="alert alert-warning">';
        $output .= '<strong>Service Status Check Unavailable</strong><br>';
        $output .= 'Your n8n service is provisioned, but we cannot check its current status at this time.';
        $output .= '</div>';
        $output .= '<div class="panel panel-default">';
        $output .= '<div class="panel-heading"><h3 class="panel-title"><i class="fa fa-cogs"></i> Your n8n Workflow Automation</h3></div>';
        $output .= '<div class="panel-body">';
        $output .= '<p><strong>Access URL:</strong> <a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-primary btn-sm"><i class="fa fa-external-link"></i> Open n8n Dashboard</a></p>';
        $output .= '<p><strong>Domain:</strong> ' . htmlspecialchars($params['domain']) . '</p>';
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
    }
}

/**
 * Single Sign-On function.
 */
function coolify_ServiceSingleSignOn($params)
{
    return array(
        'success' => true,
        'redirectTo' => 'https://' . $params['domain'],
    );
}



/**
 * Helper function to get service UUID.
 */
function getServiceUuid($serviceId)
{
    try {
        $pdo = Capsule::connection()->getPdo();
        $stmt = $pdo->prepare("SELECT value FROM tblcustomfieldsvalues WHERE relid = ? AND fieldid = (
            SELECT id FROM tblcustomfields WHERE fieldname = 'coolify_service_uuid' AND type = 'product' LIMIT 1
        )");
        $stmt->execute([$serviceId]);
        $result = $stmt->fetch(PDO::FETCH_ASSOC);

        return $result ? $result['value'] : false;
    } catch (Exception $e) {
        logModuleCall('coolify', 'getServiceUuid', ['serviceId' => $serviceId], $e->getMessage(), '');
        return false;
    }
}

/**
 * Generate random password.
 */
function generateRandomPassword($length = 16)
{
    $characters = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789!@#$%^&*';
    $password = '';
    
    for ($i = 0; $i < $length; $i++) {
        $password .= $characters[rand(0, strlen($characters) - 1)];
    }
    
    return $password;
}

/**
 * Generate n8n Docker Compose configuration
 */
function generateN8nDockerCompose($domain, $memoryLimit = '512', $n8nImage = 'n8nio/n8n:latest', $password = null)
{
    if (!$password) {
        $password = generateRandomPassword();
    }

    // Ensure memory limit is properly formatted
    $memoryLimitFormatted = intval($memoryLimit) . 'm';

    // Escape special characters for YAML
    $escapedDomain = $domain;
    $escapedPassword = $password;

    // Generate Docker Compose with proper YAML formatting
    $dockerCompose = "version: '3.8'

services:
  n8n:
    image: {$n8nImage}
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD={$escapedPassword}
      - N8N_HOST={$escapedDomain}
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - NODE_ENV=production
      - WEBHOOK_URL=https://{$escapedDomain}/
      - GENERIC_TIMEZONE=UTC
      - N8N_METRICS=true
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - coolify
    deploy:
      resources:
        limits:
          memory: {$memoryLimitFormatted}
    labels:
      - coolify.managed=true
      - traefik.enable=true
      - traefik.http.routers.n8n.rule=Host(`{$escapedDomain}`)
      - traefik.http.routers.n8n.tls=true
      - traefik.http.routers.n8n.tls.certresolver=letsencrypt
      - traefik.http.services.n8n.loadbalancer.server.port=5678

volumes:
  n8n_data:
    driver: local

networks:
  coolify:
    external: true";

    return $dockerCompose;
}
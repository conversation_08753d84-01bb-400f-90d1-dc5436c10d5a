<?php
/**
 * Debug script for Coolify API issues
 * This script helps diagnose HTTP 422 validation errors
 */

// Configuration - UPDATE THESE VALUES
$COOLIFY_URL = 'http://************:8000'; // Your Coolify URL
$API_TOKEN = '5|tapmBhK7zm6xtggr6gh9qTqrVDZMKXF79KnwpoHb6707293a'; // Your API token
$PROJECT_UUID = 'ekwcsg0cwgg8cg8ko8sks0gk'; // Your project UUID
$SERVER_UUID = 'fcw8k0ws4kc0sckc04400sog'; // Your server UUID
$DESTINATION_UUID = 'kc8oc400koso4cww0cg4sgog'; // Your destination UUID

echo "=== Coolify API Debug Tool ===\n\n";

if ($API_TOKEN === 'your-api-token-here') {
    echo "❌ Please update the configuration variables at the top of this script!\n";
    exit(1);
}

$apiUrl = rtrim($COOLIFY_URL, '/') . '/api/v1';

function makeApiRequest($url, $token, $endpoint, $method = 'GET', $data = null) {
    $fullUrl = $url . '/' . ltrim($endpoint, '/');
    
    $headers = [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $fullUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_VERBOSE => false
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $httpCode < 400,
        'code' => $httpCode,
        'response' => json_decode($response, true),
        'raw_response' => $response,
        'error' => $error
    ];
}

// Test 1: API Connection
echo "1. Testing API connection...\n";
$result = makeApiRequest($apiUrl, $API_TOKEN, '/servers');
if ($result['success']) {
    echo "✅ API connection successful\n";
    echo "   Found " . count($result['response']) . " server(s)\n\n";
} else {
    echo "❌ API connection failed\n";
    echo "   HTTP Code: " . $result['code'] . "\n";
    echo "   Error: " . ($result['error'] ?: json_encode($result['response'])) . "\n\n";
    exit(1);
}

// Test 2: Validate Project
echo "2. Validating project UUID...\n";
$result = makeApiRequest($apiUrl, $API_TOKEN, "/projects/{$PROJECT_UUID}");
if ($result['success']) {
    echo "✅ Project UUID is valid\n";
    echo "   Project: " . $result['response']['name'] . "\n\n";
} else {
    echo "❌ Invalid project UUID\n";
    echo "   HTTP Code: " . $result['code'] . "\n";
    echo "   Error: " . json_encode($result['response']) . "\n\n";
}

// Test 3: Validate Server
echo "3. Validating server UUID...\n";
$result = makeApiRequest($apiUrl, $API_TOKEN, "/servers/{$SERVER_UUID}");
if ($result['success']) {
    echo "✅ Server UUID is valid\n";
    echo "   Server: " . $result['response']['name'] . "\n\n";
} else {
    echo "❌ Invalid server UUID\n";
    echo "   HTTP Code: " . $result['code'] . "\n";
    echo "   Error: " . json_encode($result['response']) . "\n\n";
}

// Test 4: Check current Coolify API version and service creation endpoint
echo "4. Testing service creation payload...\n";

// Generate Docker Compose content
$dockerComposeContent = "version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=testpass123
      - N8N_HOST=test.example.com
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - NODE_ENV=production
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - coolify
    labels:
      - coolify.managed=true
      - traefik.enable=true
      - traefik.http.routers.n8n.rule=Host(\`test.example.com\`)
      - traefik.http.routers.n8n.tls=true
      - traefik.http.services.n8n.loadbalancer.server.port=5678

volumes:
  n8n_data:
    driver: local

networks:
  coolify:
    external: true";

// Generate test service data similar to the plugin (without 'type' field, with base64 encoding)
$testServiceData = [
    'name' => 'test-n8n-' . time(),
    'description' => 'Test n8n instance for debugging',
    'project_uuid' => $PROJECT_UUID,
    'environment_name' => 'production',
    'server_uuid' => $SERVER_UUID,
    'destination_uuid' => $DESTINATION_UUID,
    'instant_deploy' => false, // Don't actually deploy during test
    'docker_compose_raw' => base64_encode($dockerComposeContent)
];

echo "Payload being sent:\n";
echo json_encode($testServiceData, JSON_PRETTY_PRINT) . "\n\n";

$result = makeApiRequest($apiUrl, $API_TOKEN, '/services', 'POST', $testServiceData);

if ($result['success']) {
    echo "✅ Service creation payload is valid\n";
    echo "   Service UUID: " . $result['response']['uuid'] . "\n";
    echo "   Note: You may want to delete this test service from Coolify\n\n";
} else {
    echo "❌ Service creation failed\n";
    echo "   HTTP Code: " . $result['code'] . "\n";
    echo "   Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n";
    echo "   Raw Response: " . $result['raw_response'] . "\n\n";
    
    // Analyze common validation errors
    if ($result['code'] === 422) {
        echo "🔍 HTTP 422 indicates validation errors. Common issues:\n";
        echo "   - Invalid UUIDs (project, server, destination)\n";
        echo "   - Missing required fields\n";
        echo "   - Invalid Docker Compose syntax\n";
        echo "   - Destination not available on the specified server\n";
        echo "   - Environment name doesn't exist in the project\n\n";
        
        if (isset($result['response']['errors'])) {
            echo "Specific validation errors:\n";
            foreach ($result['response']['errors'] as $field => $errors) {
                echo "   $field: " . implode(', ', $errors) . "\n";
            }
        }
    }
}

// Test 5: Check available environments
echo "5. Checking available environments...\n";
$result = makeApiRequest($apiUrl, $API_TOKEN, "/projects/{$PROJECT_UUID}");
if ($result['success'] && isset($result['response']['environments'])) {
    echo "✅ Available environments:\n";
    foreach ($result['response']['environments'] as $env) {
        echo "   - " . $env['name'] . "\n";
    }
    echo "\n";
} else {
    echo "❌ Could not fetch environments from project details\n";
    // Try alternative endpoint
    $result2 = makeApiRequest($apiUrl, $API_TOKEN, "/projects/{$PROJECT_UUID}/environments");
    if ($result2['success']) {
        echo "✅ Available environments (alternative endpoint):\n";
        foreach ($result2['response'] as $env) {
            echo "   - " . (is_array($env) ? $env['name'] : $env) . "\n";
        }
        echo "\n";
    } else {
        echo "❌ Could not fetch environments from either endpoint\n\n";
    }
}

// Test 6: Check server destinations
echo "6. Checking server destinations...\n";
$result = makeApiRequest($apiUrl, $API_TOKEN, "/servers/{$SERVER_UUID}");
if ($result['success']) {
    if (isset($result['response']['destinations'])) {
        echo "✅ Available destinations:\n";
        foreach ($result['response']['destinations'] as $dest) {
            echo "   - " . $dest['uuid'] . " (" . $dest['name'] . ")\n";
        }
        echo "\n";
    } else {
        echo "ℹ️  Server details retrieved but no destinations field found\n";
        echo "   Server response keys: " . implode(', ', array_keys($result['response'])) . "\n\n";

        // Try alternative destinations endpoint
        $result2 = makeApiRequest($apiUrl, $API_TOKEN, "/servers/{$SERVER_UUID}/destinations");
        if ($result2['success']) {
            echo "✅ Available destinations (alternative endpoint):\n";
            foreach ($result2['response'] as $dest) {
                echo "   - " . $dest['uuid'] . " (" . $dest['name'] . ")\n";
            }
            echo "\n";
        } else {
            echo "❌ Could not fetch destinations from alternative endpoint either\n\n";
        }
    }
} else {
    echo "❌ Could not fetch server details\n\n";
}

echo "=== Debug Complete ===\n";
echo "If service creation failed, check the validation errors above.\n";
echo "Update your WHMCS server configuration with the correct UUIDs.\n";

<?php
/**
 * Validation script for WHMCS Coolify Plugin
 * Checks for common issues and validates the plugin structure
 */

echo "=== WHMCS Coolify Plugin Validation ===\n\n";

$errors = [];
$warnings = [];
$success = [];

// Check file structure
$requiredFiles = [
    'modules/servers/coolify/coolify.php',
    'modules/servers/coolify/hooks.php',
    'modules/servers/coolify/lib/CoolifyAPI.php',
    'modules/servers/coolify/lib/AdminFunctions.php'
];

echo "1. Checking file structure...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $success[] = "✓ $file exists";
    } else {
        $errors[] = "✗ $file missing";
    }
}

// Check for deprecated functions
echo "\n2. Checking for deprecated MySQL functions...\n";
$deprecatedFunctions = ['mysql_query', 'mysql_fetch_assoc', 'mysql_real_escape_string', 'mysql_num_rows'];

foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $found = false;
        foreach ($deprecatedFunctions as $func) {
            if (strpos($content, $func) !== false) {
                $errors[] = "✗ $file contains deprecated function: $func";
                $found = true;
            }
        }
        if (!$found) {
            $success[] = "✓ $file: No deprecated MySQL functions found";
        }
    }
}

// Check for required use statements
echo "\n3. Checking for required use statements...\n";
$filesNeedingCapsule = ['modules/servers/coolify/coolify.php', 'modules/servers/coolify/hooks.php'];

foreach ($filesNeedingCapsule as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'use Illuminate\Database\Capsule\Manager as Capsule;') !== false) {
            $success[] = "✓ $file: Capsule use statement found";
        } else {
            $errors[] = "✗ $file: Missing Capsule use statement";
        }
    }
}

// Check for proper PDO usage
echo "\n4. Checking for PDO usage...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        if (strpos($content, 'Capsule::connection()->getPdo()') !== false) {
            $success[] = "✓ $file: Uses PDO properly";
        } elseif (strpos($content, 'mysql_') !== false) {
            // Already caught above, but this is for files that should use PDO
            continue;
        }
    }
}

// Check for error handling
echo "\n5. Checking for error handling...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        $tryCount = substr_count($content, 'try {');
        $catchCount = substr_count($content, 'catch (');
        
        if ($tryCount > 0 && $catchCount >= $tryCount) {
            $success[] = "✓ $file: Has proper error handling ($tryCount try blocks)";
        } elseif ($tryCount > 0) {
            $warnings[] = "⚠ $file: Unmatched try/catch blocks";
        }
    }
}

// Check for required functions
echo "\n6. Checking for required WHMCS functions...\n";
$requiredFunctions = [
    'coolify_MetaData',
    'coolify_ConfigOptions', 
    'coolify_CreateAccount',
    'coolify_SuspendAccount',
    'coolify_UnsuspendAccount',
    'coolify_TerminateAccount',
    'coolify_TestConnection',
    'coolify_ClientArea'
];

if (file_exists('modules/servers/coolify/coolify.php')) {
    $content = file_get_contents('modules/servers/coolify/coolify.php');
    foreach ($requiredFunctions as $func) {
        if (strpos($content, "function $func(") !== false) {
            $success[] = "✓ Function $func found";
        } else {
            $errors[] = "✗ Function $func missing";
        }
    }
}

// Syntax check
echo "\n7. Running syntax checks...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $output = [];
        $return = 0;
        exec("php -l \"$file\" 2>&1", $output, $return);
        
        if ($return === 0) {
            $success[] = "✓ $file: Syntax OK";
        } else {
            $errors[] = "✗ $file: Syntax error - " . implode(' ', $output);
        }
    }
}

// Security checks
echo "\n8. Basic security checks...\n";
foreach ($requiredFiles as $file) {
    if (file_exists($file)) {
        $content = file_get_contents($file);
        
        // Check for WHMCS protection
        if (strpos($content, 'if (!defined("WHMCS"))') !== false) {
            $success[] = "✓ $file: Has WHMCS protection";
        } else {
            $warnings[] = "⚠ $file: Missing WHMCS protection check";
        }
        
        // Check for SQL injection protection (should use prepared statements)
        if (preg_match('/\$_[A-Z]+.*mysql_query|mysql_query.*\$_[A-Z]+/', $content)) {
            $errors[] = "✗ $file: Potential SQL injection vulnerability";
        }
    }
}

// Summary
echo "\n" . str_repeat("=", 50) . "\n";
echo "VALIDATION SUMMARY\n";
echo str_repeat("=", 50) . "\n";

if (!empty($success)) {
    echo "\n✅ SUCCESS (" . count($success) . " items):\n";
    foreach ($success as $item) {
        echo "  $item\n";
    }
}

if (!empty($warnings)) {
    echo "\n⚠️  WARNINGS (" . count($warnings) . " items):\n";
    foreach ($warnings as $item) {
        echo "  $item\n";
    }
}

if (!empty($errors)) {
    echo "\n❌ ERRORS (" . count($errors) . " items):\n";
    foreach ($errors as $item) {
        echo "  $item\n";
    }
    echo "\n🚨 CRITICAL: Plugin has errors that must be fixed before use!\n";
} else {
    echo "\n🎉 EXCELLENT: No critical errors found!\n";
    echo "The plugin appears to be ready for installation.\n";
}

echo "\nNext steps:\n";
echo "1. Review any warnings above\n";
echo "2. Test the plugin in a staging environment\n";
echo "3. Verify Coolify API connectivity\n";
echo "4. Create test services to ensure functionality\n";
echo "\nFor detailed setup instructions, see SETUP_GUIDE.md\n";

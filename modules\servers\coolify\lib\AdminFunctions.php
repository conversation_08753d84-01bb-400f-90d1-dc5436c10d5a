<?php
/**
 * Admin Functions for Coolify Module
 */

if (!defined("WHMCS")) {
    die("This file cannot be accessed directly");
}

require_once __DIR__ . '/CoolifyAPI.php';

/**
 * View Service Details
 */
function coolify_viewService($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'API token not configured';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);
        
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $serviceInfo = $api->getService($serviceUuid);
        $serverResources = $api->getServerResources($params['configoption4']);
        
        $output = '<div class="panel panel-default">';
        $output .= '<div class="panel-heading"><h4>n8n Service Management</h4></div>';
        $output .= '<div class="panel-body">';
        
        if ($serviceInfo) {
            $output .= '<div class="row">';
            $output .= '<div class="col-md-6">';
            $output .= '<strong>n8n Instance Details:</strong><br>';
            $output .= 'Service Name: ' . htmlspecialchars($serviceInfo['name']) . '<br>';
            $output .= 'Status: <span class="label label-' . 
                       ($serviceInfo['status'] == 'running' ? 'success' : 'warning') . '">' . 
                       htmlspecialchars($serviceInfo['status']) . '</span><br>';
            $output .= 'Created: ' . (isset($serviceInfo['created_at']) ? date('Y-m-d H:i:s', strtotime($serviceInfo['created_at'])) : 'N/A') . '<br>';
            $output .= 'Last Updated: ' . (isset($serviceInfo['updated_at']) ? date('Y-m-d H:i:s', strtotime($serviceInfo['updated_at'])) : 'N/A') . '<br>';
            $output .= '</div>';
            
            $output .= '<div class="col-md-6">';
            $output .= '<strong>Customer Access:</strong><br>';
            $output .= 'n8n URL: <a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank">https://' . htmlspecialchars($params['domain']) . '</a><br>';
            $output .= 'Login: admin<br>';
            $output .= 'Password: [Generated automatically]<br>';
            $output .= '<br><strong>Admin Actions:</strong><br>';
            $output .= '<a href="https://' . htmlspecialchars($params['domain']) . '" target="_blank" class="btn btn-sm btn-primary">Open n8n Dashboard</a>';
            $output .= '</div>';
            $output .= '</div>';
            
            // Show environment variables if available
            if (isset($serviceInfo['environment_variables']) && !empty($serviceInfo['environment_variables'])) {
                $output .= '<hr>';
                $output .= '<strong>Environment Variables:</strong><br>';
                $output .= '<table class="table table-condensed">';
                foreach ($serviceInfo['environment_variables'] as $key => $value) {
                    $displayValue = (strpos($key, 'PASSWORD') !== false || strpos($key, 'SECRET') !== false) ? '••••••••' : $value;
                    $output .= '<tr><td>' . htmlspecialchars($key) . '</td><td>' . htmlspecialchars($displayValue) . '</td></tr>';
                }
                $output .= '</table>';
            }
        } else {
            $output .= '<p class="text-warning">Unable to retrieve service information.</p>';
        }
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
        
    } catch (Exception $e) {
        logModuleCall('coolify', 'viewService', $params, $e->getMessage(), $e->getTraceAsString());
        return '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Restart Service
 */
function coolify_restartService($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'API token not configured';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);
        
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $result = $api->restartService($serviceUuid);
        
        logModuleCall('coolify', 'restartService', $params, $result, '', array($apiToken));
        
        if ($result) {
            return '<div class="alert alert-success">n8n service restart initiated successfully. It may take a few moments to complete.</div>';
        } else {
            return '<div class="alert alert-warning">n8n restart request sent, but no confirmation received.</div>';
        }
        
    } catch (Exception $e) {
        logModuleCall('coolify', 'restartService', $params, $e->getMessage(), $e->getTraceAsString());
        return '<div class="alert alert-danger">Error restarting service: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * View Service Logs
 */
function coolify_viewLogs($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'API token not configured';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);
        
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        $logs = $api->getServiceLogs($serviceUuid, 200);
        
        $output = '<div class="panel panel-default">';
        $output .= '<div class="panel-heading">';
        $output .= '<h4>n8n Service Logs <small>(Last 200 lines)</small></h4>';
        $output .= '</div>';
        $output .= '<div class="panel-body" style="max-height: 500px; overflow-y: auto;">';
        
        if ($logs && is_array($logs)) {
            $output .= '<pre style="background: #000; color: #fff; padding: 15px; font-size: 12px;">';
            foreach ($logs as $logLine) {
                if (is_string($logLine)) {
                    $output .= htmlspecialchars($logLine) . "\n";
                } elseif (isset($logLine['message'])) {
                    $timestamp = isset($logLine['timestamp']) ? '[' . $logLine['timestamp'] . '] ' : '';
                    $output .= htmlspecialchars($timestamp . $logLine['message']) . "\n";
                }
            }
            $output .= '</pre>';
        } elseif (is_string($logs)) {
            $output .= '<pre style="background: #000; color: #fff; padding: 15px; font-size: 12px;">';
            $output .= htmlspecialchars($logs);
            $output .= '</pre>';
        } else {
            $output .= '<p class="text-muted">No logs available or unable to retrieve logs.</p>';
        }
        
        $output .= '</div>';
        $output .= '<div class="panel-footer">';
        $output .= '<small class="text-muted">Logs are refreshed each time you view this page. These show the n8n container output and any errors.</small>';
        $output .= '</div>';
        $output .= '</div>';
        
        logModuleCall('coolify', 'viewLogs', $params, 'Logs retrieved successfully', '');
        
        return $output;
        
    } catch (Exception $e) {
        logModuleCall('coolify', 'viewLogs', $params, $e->getMessage(), $e->getTraceAsString());
        return '<div class="alert alert-danger">Error retrieving logs: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Update Service Configuration
 */
function coolify_updateConfig($params)
{
    try {
        // Use server access hash as primary API token, fallback to config option
        $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
        
        if (empty($apiToken)) {
            return 'API token not configured';
        }
        
        // Get Coolify instance URL from configuration
        $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
        $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
        
        $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
        $serviceUuid = getServiceUuid($params['serviceid']);
        
        if (!$serviceUuid) {
            return 'Service UUID not found';
        }
        
        // Get current service info
        $serviceInfo = $api->getService($serviceUuid);
        
        $output = '<div class="panel panel-default">';
        $output .= '<div class="panel-heading"><h4>Update n8n Service Configuration</h4></div>';
        $output .= '<div class="panel-body">';
        
        $output .= '<form method="post" action="">';
        $output .= '<input type="hidden" name="action" value="updateServiceConfig">';
        $output .= '<input type="hidden" name="serviceid" value="' . $params['serviceid'] . '">';
        
        $output .= '<div class="form-group">';
        $output .= '<label>Memory Limit (MB):</label>';
        $currentMemory = isset($serviceInfo['memory_limit']) ? $serviceInfo['memory_limit'] : $params['configoption8'];
        $output .= '<input type="number" name="memory_limit" class="form-control" value="' . htmlspecialchars($currentMemory) . '" min="128" max="4096">';
        $output .= '</div>';
        
        $output .= '<div class="form-group">';
        $output .= '<label>Environment Variables:</label>';
        $output .= '<textarea name="env_vars" class="form-control" rows="8" placeholder="KEY1=value1&#10;KEY2=value2">';
        if (isset($serviceInfo['environment_variables']) && is_array($serviceInfo['environment_variables'])) {
            foreach ($serviceInfo['environment_variables'] as $key => $value) {
                $output .= htmlspecialchars($key . '=' . $value) . "\n";
            }
        }
        $output .= '</textarea>';
        $output .= '<small class="help-block">One environment variable per line in KEY=value format</small>';
        $output .= '</div>';
        
        $output .= '<button type="submit" class="btn btn-primary">Update Configuration</button>';
        $output .= '</form>';
        
        $output .= '</div>';
        $output .= '</div>';
        
        return $output;
        
    } catch (Exception $e) {
        return '<div class="alert alert-danger">Error: ' . htmlspecialchars($e->getMessage()) . '</div>';
    }
}

/**
 * Handle service configuration updates
 */
function coolify_handleConfigUpdate($params)
{
    if ($_POST['action'] == 'updateServiceConfig' && $_POST['serviceid'] == $params['serviceid']) {
        try {
            // Use server access hash as primary API token, fallback to config option
            $apiToken = !empty($params['serveraccesshash']) ? $params['serveraccesshash'] : $params['configoption1'];
            
            if (empty($apiToken)) {
                return 'API token not configured';
            }
            
            // Get Coolify instance URL from configuration
            $coolifyUrl = !empty($params['configoption9']) ? $params['configoption9'] : 'https://app.coolify.io';
            $coolifyApiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
            
            $api = new CoolifyAPI($apiToken, $coolifyApiUrl);
            $serviceUuid = getServiceUuid($params['serviceid']);
            
            if (!$serviceUuid) {
                return 'Service UUID not found';
            }
            
            $updateData = array();
            
            // Update memory limit if provided
            if (isset($_POST['memory_limit']) && is_numeric($_POST['memory_limit'])) {
                $updateData['memory_limit'] = intval($_POST['memory_limit']);
            }
            
            // Update environment variables if provided
            if (isset($_POST['env_vars']) && !empty($_POST['env_vars'])) {
                $envVars = array();
                $lines = explode("\n", $_POST['env_vars']);
                foreach ($lines as $line) {
                    $line = trim($line);
                    if (!empty($line) && strpos($line, '=') !== false) {
                        list($key, $value) = explode('=', $line, 2);
                        $envVars[trim($key)] = trim($value);
                    }
                }
                if (!empty($envVars)) {
                    $api->setServiceEnvironmentVariables($serviceUuid, $envVars);
                }
            }
            
            if (!empty($updateData)) {
                $result = $api->updateService($serviceUuid, $updateData);
            }
            
            return '<div class="alert alert-success">n8n service configuration updated successfully.</div>';
            
        } catch (Exception $e) {
            logModuleCall('coolify', 'updateConfig', $params, $e->getMessage(), $e->getTraceAsString());
            return '<div class="alert alert-danger">Error updating configuration: ' . htmlspecialchars($e->getMessage()) . '</div>';
        }
    }
    
    return coolify_updateConfig($params);
} 
<?php
/**
 * Quick Fix Script for HTTP 422 Errors
 * This script helps identify and fix common configuration issues
 */

echo "=== Coolify Plugin HTTP 422 Error Fix Tool ===\n\n";

// Step 1: Check if we're in WHMCS environment
if (!file_exists('configuration.php')) {
    echo "❌ This script should be run from your WHMCS root directory\n";
    echo "   Please copy this script to your WHMCS root and run it there.\n\n";
    exit(1);
}

// Step 2: Load WHMCS
require_once 'init.php';

use Illuminate\Database\Capsule\Manager as Capsule;

echo "✅ WHMCS environment loaded\n\n";

// Step 3: Find Coolify servers
echo "1. Checking for Coolify servers...\n";
try {
    $pdo = Capsule::connection()->getPdo();
    $stmt = $pdo->prepare("SELECT * FROM tblservers WHERE type = 'coolify'");
    $stmt->execute();
    $servers = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($servers)) {
        echo "❌ No Coolify servers found\n";
        echo "   Please create a server with type 'Coolify n8n' first\n\n";
        exit(1);
    }
    
    echo "✅ Found " . count($servers) . " Coolify server(s)\n\n";
    
    foreach ($servers as $server) {
        echo "Server: " . $server['name'] . " (ID: " . $server['id'] . ")\n";
        echo "Hostname: " . $server['hostname'] . "\n";
        echo "Access Hash: " . (empty($server['accesshash']) ? "❌ MISSING" : "✅ Set") . "\n\n";
    }
    
} catch (Exception $e) {
    echo "❌ Database error: " . $e->getMessage() . "\n\n";
    exit(1);
}

// Step 4: Check server configurations
echo "2. Validating server configurations...\n";

foreach ($servers as $server) {
    echo "Checking server: " . $server['name'] . "\n";
    
    // Get server configuration
    $stmt = $pdo->prepare("SELECT * FROM tblservers WHERE id = ?");
    $stmt->execute([$server['id']]);
    $serverConfig = $stmt->fetch(PDO::FETCH_ASSOC);
    
    // Check access hash (API token)
    if (empty($serverConfig['accesshash'])) {
        echo "❌ Missing API token in Access Hash field\n";
        continue;
    }
    
    // Get module configuration options
    $stmt = $pdo->prepare("SELECT * FROM tblproducts WHERE servertype = 'coolify' AND server = ?");
    $stmt->execute([$server['id']]);
    $products = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($products)) {
        echo "⚠️  No products using this server\n";
        continue;
    }
    
    foreach ($products as $product) {
        echo "  Product: " . $product['name'] . "\n";
        
        // Get product configuration
        $stmt = $pdo->prepare("SELECT * FROM tblproductconfigoptions WHERE gid = ?");
        $stmt->execute([$product['configoption1']]);
        $configOptions = $stmt->fetchAll(PDO::FETCH_ASSOC);
        
        // Check required configurations
        $requiredConfigs = [
            'Project UUID' => 'configoption2',
            'Environment Name' => 'configoption3', 
            'Server UUID' => 'configoption4',
            'Destination UUID' => 'configoption5',
            'Base Domain' => 'configoption6'
        ];
        
        foreach ($requiredConfigs as $name => $field) {
            $value = $product[$field] ?? '';
            if (empty($value)) {
                echo "    ❌ Missing: $name\n";
            } else {
                echo "    ✅ $name: " . substr($value, 0, 20) . "...\n";
            }
        }
    }
    
    echo "\n";
}

// Step 5: Test API connectivity
echo "3. Testing API connectivity...\n";

foreach ($servers as $server) {
    if (empty($server['accesshash'])) {
        continue;
    }
    
    echo "Testing server: " . $server['name'] . "\n";
    
    // Get Coolify URL
    $coolifyUrl = $server['hostname'];
    if (!preg_match('/^https?:\/\//', $coolifyUrl)) {
        $coolifyUrl = 'https://' . $coolifyUrl;
    }
    $apiUrl = rtrim($coolifyUrl, '/') . '/api/v1';
    
    // Test API call
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $apiUrl . '/servers',
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_TIMEOUT => 10,
        CURLOPT_HTTPHEADER => [
            'Authorization: Bearer ' . $server['accesshash'],
            'Accept: application/json'
        ],
        CURLOPT_SSL_VERIFYPEER => false
    ]);
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    if ($error) {
        echo "  ❌ Connection error: $error\n";
    } elseif ($httpCode === 200) {
        $data = json_decode($response, true);
        echo "  ✅ API connection successful\n";
        echo "  📊 Found " . (is_array($data) ? count($data) : 0) . " server(s) in Coolify\n";
    } elseif ($httpCode === 401) {
        echo "  ❌ Authentication failed - check API token\n";
    } else {
        echo "  ❌ HTTP $httpCode: $response\n";
    }
    
    echo "\n";
}

// Step 6: Check for recent errors
echo "4. Checking recent module logs...\n";

try {
    $stmt = $pdo->prepare("SELECT * FROM tblmodulelog WHERE module = 'coolify' ORDER BY date DESC LIMIT 10");
    $stmt->execute();
    $logs = $stmt->fetchAll(PDO::FETCH_ASSOC);
    
    if (empty($logs)) {
        echo "ℹ️  No recent module logs found\n\n";
    } else {
        echo "Recent errors:\n";
        foreach ($logs as $log) {
            echo "  " . $log['date'] . " - " . $log['action'] . "\n";
            if (!empty($log['responsedata'])) {
                $response = substr($log['responsedata'], 0, 100);
                echo "    Response: $response...\n";
            }
            echo "\n";
        }
    }
} catch (Exception $e) {
    echo "⚠️  Could not check module logs: " . $e->getMessage() . "\n\n";
}

// Step 7: Provide recommendations
echo "=== RECOMMENDATIONS ===\n\n";

echo "Based on the analysis above, here's what to do:\n\n";

echo "1. **Fix Missing Configurations**\n";
echo "   - Go to WHMCS Admin → Setup → Products/Services → Servers\n";
echo "   - Edit your Coolify server and fill in all required fields\n";
echo "   - Use the debug script to get correct UUIDs\n\n";

echo "2. **Test API Connection**\n";
echo "   - Use the 'Test Connection' button in server settings\n";
echo "   - If it fails, check your API token and Coolify URL\n\n";

echo "3. **Check Coolify Instance**\n";
echo "   - Ensure your Coolify instance is accessible\n";
echo "   - Verify the project, server, and destination exist\n";
echo "   - Check that the environment name is correct\n\n";

echo "4. **Debug Further**\n";
echo "   - Run debug_coolify_api.php with your actual values\n";
echo "   - Check WHMCS Module Logs for detailed errors\n";
echo "   - Review TROUBLESHOOT_422_ERROR.md guide\n\n";

echo "5. **Common Quick Fixes**\n";
echo "   - Environment name: Use 'production' (lowercase)\n";
echo "   - UUIDs: Copy directly from Coolify dashboard URLs\n";
echo "   - API token: Generate new one if authentication fails\n";
echo "   - Base domain: Ensure DNS is configured correctly\n\n";

echo "=== NEXT STEPS ===\n";
echo "1. Fix any missing configurations identified above\n";
echo "2. Test the connection in WHMCS server settings\n";
echo "3. Try creating a test service\n";
echo "4. If still failing, run debug_coolify_api.php for detailed analysis\n\n";

echo "For detailed troubleshooting, see: TROUBLESHOOT_422_ERROR.md\n";

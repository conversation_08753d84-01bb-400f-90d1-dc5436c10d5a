<?php
/**
 * Quick test to verify the API fix works
 */

// Configuration - UPDATE THESE VALUES
$COOLIFY_URL = 'https://your-coolify-instance.com'; // Your Coolify URL
$API_TOKEN = 'your-api-token-here'; // Your API token
$PROJECT_UUID = 'your-project-uuid'; // Your project UUID
$SERVER_UUID = 'your-server-uuid'; // Your server UUID
$DESTINATION_UUID = 'your-destination-uuid'; // Your destination UUID

echo "=== Testing API Fix for HTTP 422 Error ===\n\n";

if ($API_TOKEN === 'your-api-token-here') {
    echo "❌ Please update the configuration variables at the top of this script!\n";
    exit(1);
}

$apiUrl = rtrim($COOLIFY_URL, '/') . '/api/v1';

function makeApiRequest($url, $token, $endpoint, $method = 'GET', $data = null) {
    $fullUrl = $url . '/' . ltrim($endpoint, '/');
    
    $headers = [
        'Authorization: Bearer ' . $token,
        'Content-Type: application/json',
        'Accept: application/json'
    ];
    
    $ch = curl_init();
    curl_setopt_array($ch, [
        CURLOPT_URL => $fullUrl,
        CURLOPT_RETURNTRANSFER => true,
        CURLOPT_FOLLOWLOCATION => true,
        CURLOPT_SSL_VERIFYPEER => false,
        CURLOPT_TIMEOUT => 30,
        CURLOPT_HTTPHEADER => $headers,
        CURLOPT_CUSTOMREQUEST => $method,
        CURLOPT_VERBOSE => false
    ]);
    
    if ($data && in_array($method, ['POST', 'PUT', 'PATCH'])) {
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($data));
    }
    
    $response = curl_exec($ch);
    $httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
    $error = curl_error($ch);
    curl_close($ch);
    
    return [
        'success' => $httpCode < 400,
        'code' => $httpCode,
        'response' => json_decode($response, true),
        'raw_response' => $response,
        'error' => $error
    ];
}

echo "Testing service creation with FIXED payload (no 'type' field)...\n\n";

// Generate test service data WITHOUT the problematic 'type' field
$testServiceData = [
    'name' => 'test-n8n-fixed-' . time(),
    'description' => 'Test n8n instance with fixed API payload',
    'project_uuid' => $PROJECT_UUID,
    'environment_name' => 'production',
    'server_uuid' => $SERVER_UUID,
    'destination_uuid' => $DESTINATION_UUID,
    'instant_deploy' => false, // Don't actually deploy during test
    'docker_compose_raw' => "version: '3.8'

services:
  n8n:
    image: n8nio/n8n:latest
    restart: unless-stopped
    environment:
      - N8N_BASIC_AUTH_ACTIVE=true
      - N8N_BASIC_AUTH_USER=admin
      - N8N_BASIC_AUTH_PASSWORD=testpass123
      - N8N_HOST=test.example.com
      - N8N_PORT=5678
      - N8N_PROTOCOL=https
      - NODE_ENV=production
    volumes:
      - n8n_data:/home/<USER>/.n8n
    networks:
      - coolify
    labels:
      - coolify.managed=true
      - traefik.enable=true
      - traefik.http.routers.n8n.rule=Host(\`test.example.com\`)
      - traefik.http.routers.n8n.tls=true
      - traefik.http.services.n8n.loadbalancer.server.port=5678

volumes:
  n8n_data:
    driver: local

networks:
  coolify:
    external: true"
];

echo "Payload being sent (FIXED VERSION):\n";
echo json_encode($testServiceData, JSON_PRETTY_PRINT) . "\n\n";

$result = makeApiRequest($apiUrl, $API_TOKEN, '/services', 'POST', $testServiceData);

if ($result['success']) {
    echo "🎉 SUCCESS! Service creation payload is now valid\n";
    echo "   Service UUID: " . $result['response']['uuid'] . "\n";
    echo "   Status: " . ($result['response']['status'] ?? 'Unknown') . "\n\n";
    
    echo "✅ The HTTP 422 error has been FIXED!\n";
    echo "   Your WHMCS plugin should now work correctly.\n\n";
    
    echo "⚠️  IMPORTANT: You may want to delete this test service from Coolify\n";
    echo "   Service name: " . $testServiceData['name'] . "\n\n";
    
} else {
    echo "❌ Service creation still failed\n";
    echo "   HTTP Code: " . $result['code'] . "\n";
    echo "   Response: " . json_encode($result['response'], JSON_PRETTY_PRINT) . "\n\n";
    
    if ($result['code'] === 422) {
        echo "🔍 Still getting HTTP 422. Remaining validation errors:\n";
        if (isset($result['response']['errors'])) {
            foreach ($result['response']['errors'] as $field => $errors) {
                echo "   $field: " . implode(', ', $errors) . "\n";
            }
        }
        echo "\n";
    }
}

echo "=== Test Complete ===\n";
echo "If successful, your WHMCS plugin should now work!\n";
echo "Try creating a service through WHMCS to confirm.\n";

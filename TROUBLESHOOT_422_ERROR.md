# Troubleshooting HTTP 422 Validation Error

## Overview
HTTP 422 "Unprocessable Entity" indicates that the Coolify API received your request but couldn't process it due to validation errors. This guide will help you identify and fix the specific issues.

## Quick Diagnosis Steps

### Step 1: Check WHMCS Module Logs
1. Go to **WHMCS Admin → Utilities → Logs → Module Log**
2. Look for recent "coolify" entries
3. Check for detailed error messages in the response

### Step 2: Verify Server Configuration
1. Go to **WHMCS Admin → Setup → Products/Services → Servers**
2. Edit your Coolify server
3. Verify all fields are correctly filled:

**Required Fields:**
- ✅ **Access Hash**: Your Coolify API Bearer Token
- ✅ **Project UUID**: Valid UUID from your Coolify project
- ✅ **Environment Name**: Must exist in your Coolify project (usually "production")
- ✅ **Server UUID**: Valid UUID from your Coolify server
- ✅ **Destination UUID**: Valid UUID from your server's destinations
- ✅ **Base Domain**: Domain you control (e.g., customers.yourdomain.com)
- ✅ **Coolify Instance URL**: Your Coolify URL without /api/v1

### Step 3: Use the Debug Script
1. Update `debug_coolify_api.php` with your actual values
2. Run: `php debug_coolify_api.php`
3. This will test each component and show specific validation errors

## Common Causes and Solutions

### 1. Invalid UUIDs
**Symptoms**: "Invalid project_uuid", "Invalid server_uuid", etc.
**Solution**: 
- Copy UUIDs directly from Coolify dashboard URLs
- Ensure no extra spaces or characters
- UUIDs should be in format: `xxxxxxxx-xxxx-xxxx-xxxx-xxxxxxxxxxxx`

**How to find UUIDs:**
- **Project UUID**: Go to Projects → Click your project → Copy from URL
- **Server UUID**: Go to Servers → Click your server → Copy from URL  
- **Destination UUID**: In server details → Destinations tab → Copy UUID

### 2. Environment Doesn't Exist
**Symptoms**: "Environment not found"
**Solution**:
- Check available environments in your Coolify project
- Default is usually "production"
- Create the environment in Coolify if it doesn't exist

### 3. Destination Not Available
**Symptoms**: "Destination not found" or "Destination not available on server"
**Solution**:
- Verify the destination UUID belongs to the specified server
- Check that the destination is active and healthy
- Ensure Docker is running on the destination

### 4. Invalid Docker Compose
**Symptoms**: "Invalid docker_compose_raw" or YAML parsing errors
**Solution**:
- The plugin generates Docker Compose automatically
- Check for special characters in domain names
- Ensure domain follows DNS naming conventions

### 5. API Token Issues
**Symptoms**: "Unauthorized" or "Invalid token"
**Solution**:
- Generate a new API token in Coolify
- Ensure token has proper permissions
- Copy token exactly without extra spaces

### 6. Network/Connectivity Issues
**Symptoms**: Connection timeouts or network errors
**Solution**:
- Verify WHMCS server can reach your Coolify instance
- Check firewall rules
- Test with curl from WHMCS server

## Detailed Debugging

### Enable Detailed Logging
Add this to your WHMCS configuration.php:
```php
$debug_log = true;
```

### Manual API Test
Test the API manually with curl:
```bash
curl -X POST "https://your-coolify.com/api/v1/services" \
  -H "Authorization: Bearer YOUR_API_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "test-service",
    "project_uuid": "YOUR_PROJECT_UUID",
    "environment_name": "production",
    "server_uuid": "YOUR_SERVER_UUID", 
    "destination_uuid": "YOUR_DESTINATION_UUID",
    "docker_compose_raw": "version: '\''3.8'\''\nservices:\n  test:\n    image: nginx:latest"
  }'
```

### Check Coolify Logs
In your Coolify instance:
1. Go to the server where you're trying to deploy
2. Check server logs for any errors
3. Verify the destination is healthy

## Configuration Checklist

Before creating services, verify:

- [ ] API token is valid and has permissions
- [ ] Project UUID exists and is accessible
- [ ] Environment exists in the project
- [ ] Server UUID is correct and server is online
- [ ] Destination UUID belongs to the server
- [ ] Destination is healthy and has Docker running
- [ ] Base domain DNS is configured correctly
- [ ] Coolify instance URL is accessible from WHMCS
- [ ] No firewall blocking communication

## Updated Plugin Features

The plugin now includes:

### Enhanced Error Reporting
- Detailed validation error messages
- Better logging for debugging
- Configuration validation before API calls

### Improved API Calls
- Proper field validation
- Better error handling
- More robust Docker Compose generation

### Debug Tools
- `debug_coolify_api.php` - Comprehensive API testing
- `validate_plugin.php` - Plugin validation
- Enhanced WHMCS module logging

## Getting Help

### Information to Collect
When seeking help, provide:

1. **WHMCS Module Log entries** (with API tokens redacted)
2. **Coolify version** and instance URL
3. **Server configuration** (UUIDs and settings)
4. **Output from debug script**
5. **Any error messages** from Coolify dashboard

### Common Solutions Summary

| Error | Most Likely Cause | Quick Fix |
|-------|------------------|-----------|
| Invalid project_uuid | Wrong UUID | Copy from Coolify project URL |
| Invalid server_uuid | Wrong UUID | Copy from Coolify server URL |
| Invalid destination_uuid | Wrong UUID or server mismatch | Check server destinations |
| Environment not found | Typo or missing environment | Use "production" or create environment |
| Unauthorized | Invalid API token | Generate new token in Coolify |
| Connection failed | Network/firewall issue | Test connectivity with curl |

## Next Steps

1. **Fix Configuration**: Update server settings with correct values
2. **Test Connection**: Use the "Test Connection" button in WHMCS
3. **Run Debug Script**: Use `debug_coolify_api.php` for detailed testing
4. **Create Test Service**: Try creating a service manually
5. **Check Logs**: Monitor both WHMCS and Coolify logs

The updated plugin provides much better error reporting, so you should now see specific validation errors that will help identify exactly what needs to be fixed.
